import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import User from '@/models/User';
import { verifyPassword, generateToken } from '@/lib/auth';
import { validateEmail } from '@/lib/utils';
import fs from 'fs';
import path from 'path';

export async function POST(request: NextRequest) {
  try {
    await connectDB();

    const body = await request.json();
    const { email, password } = body;

    // Validation
    if (!email || !password) {
      return NextResponse.json(
        { error: 'Email and password are required' },
        { status: 400 }
      );
    }

    if (!validateEmail(email)) {
      return NextResponse.json(
        { error: 'Please provide a valid email address' },
        { status: 400 }
      );
    }

    // Find user by email
    const user = await User.findOne({ email: email.toLowerCase() });
    if (!user) {
      return NextResponse.json(
        { error: 'Invalid email or password' },
        { status: 401 }
      );
    }

    // Check if user is active
    if (!user.isActive) {
      return NextResponse.json(
        { error: 'Account is deactivated. Please contact support.' },
        { status: 401 }
      );
    }

    // Verify password
    const isPasswordValid = await verifyPassword(password, user.password);
    if (!isPasswordValid) {
      return NextResponse.json(
        { error: 'Invalid email or password' },
        { status: 401 }
      );
    }

    // Generate JWT token
    const token = generateToken({
      userId: user._id.toString(),
      email: user.email,
      role: user.role
    });

    // Save token to .env file
    const envPath = path.join(process.cwd(), '.env.local');
    let envContent = '';

    try {
      envContent = fs.readFileSync(envPath, 'utf8');
    } catch (error) {
      // File doesn't exist, create new content
    }

    // Remove existing USER_TOKEN if present
    const lines = envContent.split('\n').filter(line => !line.startsWith('USER_TOKEN='));

    // Add new token
    lines.push(`USER_TOKEN=${token}`);

    // Write back to file
    fs.writeFileSync(envPath, lines.join('\n'));

    // Return user data (without password) and success message
    const userData = {
      id: user._id,
      name: user.name,
      email: user.email,
      phone: user.phone,
      dealership: user.dealership,
      role: user.role,
      isActive: user.isActive,
      createdAt: user.createdAt
    };

    const response = NextResponse.json(
      {
        message: 'Login successful! Redirecting to dashboard...',
        user: userData,
        token: token,
        success: true
      },
      { status: 200 }
    );

    // Also set token as HTTP-only cookie for browser persistence
    response.cookies.set('auth_token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 30 * 24 * 60 * 60 // 30 days
    });

    return response;

  } catch (error) {
    console.error('Login error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
