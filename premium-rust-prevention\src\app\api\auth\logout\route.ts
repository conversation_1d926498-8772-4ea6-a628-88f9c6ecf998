import { NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

export async function POST() {
  try {
    // Remove token from .env file
    const envPath = path.join(process.cwd(), '.env.local');
    let envContent = '';

    try {
      envContent = fs.readFileSync(envPath, 'utf8');
    } catch (error) {
      // File doesn't exist, nothing to do
      return NextResponse.json(
        { message: 'Logout successful' },
        { status: 200 }
      );
    }

    // Remove USER_TOKEN line
    const lines = envContent.split('\n').filter(line => !line.startsWith('USER_TOKEN='));

    // Write back to file
    fs.writeFileSync(envPath, lines.join('\n'));

    const response = NextResponse.json(
      { message: 'Logout successful' },
      { status: 200 }
    );

    // Clear the auth cookie
    response.cookies.set('auth_token', '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 0 // Expire immediately
    });

    return response;

  } catch (error) {
    console.error('Logout error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
