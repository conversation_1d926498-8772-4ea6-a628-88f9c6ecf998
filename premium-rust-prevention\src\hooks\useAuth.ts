'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { User, getUserData, getAuthToken, clearAuthData } from '@/utils/auth';

export const useAuth = () => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    const token = getAuthToken();
    const userData = getUserData();

    if (token && userData) {
      setUser(userData);
    }
    
    setIsLoading(false);
  }, []);

  const logout = async () => {
    try {
      await fetch('/api/auth/logout', { method: 'POST' });
    } catch (error) {
      console.error('Logout error:', error);
    }
    
    clearAuthData();
    setUser(null);
    router.push('/login');
  };

  const updateUser = (userData: User) => {
    setUser(userData);
  };

  return {
    user,
    isLoading,
    isAuthenticated: !!user,
    logout,
    updateUser
  };
};
