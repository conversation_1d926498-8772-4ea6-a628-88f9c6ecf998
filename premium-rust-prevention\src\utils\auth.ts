export interface User {
  id: string;
  name: string;
  email: string;
  phone: string;
  dealership: string;
  role: string;
  isActive: boolean;
  createdAt: string;
}

export const getAuthToken = (): string | null => {
  if (typeof window === 'undefined') return null;
  return localStorage.getItem('auth_token');
};

export const getUserData = (): User | null => {
  if (typeof window === 'undefined') return null;
  
  const userData = localStorage.getItem('user_data');
  if (!userData) return null;
  
  try {
    return JSON.parse(userData);
  } catch (error) {
    console.error('Error parsing user data:', error);
    // Clear invalid data
    localStorage.removeItem('auth_token');
    localStorage.removeItem('user_data');
    return null;
  }
};

export const setAuthData = (token: string, user: User): void => {
  if (typeof window === 'undefined') return;
  
  localStorage.setItem('auth_token', token);
  localStorage.setItem('user_data', JSON.stringify(user));
};

export const clearAuthData = (): void => {
  if (typeof window === 'undefined') return;
  
  localStorage.removeItem('auth_token');
  localStorage.removeItem('user_data');
};

export const isAuthenticated = (): boolean => {
  return !!(getAuthToken() && getUserData());
};
