import React from 'react';
import Image from 'next/image';

interface LogoProps {
  size?: 'sm' | 'md' | 'lg';
  showText?: boolean;
  className?: string;
}

const Logo: React.FC<LogoProps> = ({ size = 'md', showText = true, className = '' }) => {
  const sizeClasses = {
    sm: 'w-10 h-10',
    md: 'w-16 h-16',
    lg: 'w-24 h-24'
  };

  const textSizeClasses = {
    sm: 'text-sm',
    md: 'text-lg',
    lg: 'text-2xl'
  };

  return (
    <div className={`flex items-center space-x-3 ${className}`}>
      {/* PRP Logo Image */}
      <div className={`${sizeClasses[size]} relative`}>
        <Image
          src="/PRP LOGO.jpg"
          alt="Premium Rust Prevention Logo"
          width={size === 'sm' ? 40 : size === 'md' ? 64 : 96}
          height={size === 'sm' ? 40 : size === 'md' ? 64 : 96}
          className="w-full h-full object-contain rounded-lg"
          priority
        />
      </div>

      {/* Company Name */}
      {showText && (
        <div className="flex flex-col">
          <span className={`${textSizeClasses[size]} font-bold text-prp-gold leading-tight`}>
            PREMIUM RUST
          </span>
          <span className={`${textSizeClasses[size]} font-bold text-prp-silver leading-tight`}>
            PREVENTION
          </span>
        </div>
      )}
    </div>
  );
};

export default Logo;
