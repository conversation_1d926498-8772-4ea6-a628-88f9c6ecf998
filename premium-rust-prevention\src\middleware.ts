import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { verifyToken } from '@/lib/auth';
import fs from 'fs';
import path from 'path';

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Only check token for login route - if user has valid token, redirect to dashboard
  if (pathname === '/login') {
    // Get token from cookies first, then .env file as fallback
    let token = request.cookies.get('auth_token')?.value;

    if (!token) {
      // Fallback to .env file
      try {
        const envPath = path.join(process.cwd(), '.env.local');
        const envContent = fs.readFileSync(envPath, 'utf8');
        const tokenLine = envContent.split('\n').find(line => line.startsWith('USER_TOKEN='));
        if (tokenLine) {
          token = tokenLine.split('=')[1];
        }
      } catch (error) {
        // No token found, allow access to login
      }
    }

    // If user has valid token and trying to access login, redirect to dashboard
    if (token) {
      const payload = verifyToken(token);
      if (payload) {
        return NextResponse.redirect(new URL('/dashboard', request.url));
      }
    }
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|public).*)',
  ],
};
