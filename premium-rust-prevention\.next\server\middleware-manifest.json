{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "YuCcEs1eRiOtMzh5YB9dQqOysIh9LOLm/7pCTTsqVI0=", "__NEXT_PREVIEW_MODE_ID": "95d37761d6d7fc221b4a3a0e8219386d", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "9303dff90da6915cd393c7e89231b1c15d557085cae9496ce8fc1ae2c5b1e8fa", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7ec48cc6660130c91e9f0fb2e140af739acbb6f48e7d2d088caf64474a075d91"}}}, "functions": {}, "sortedMiddleware": ["/"]}