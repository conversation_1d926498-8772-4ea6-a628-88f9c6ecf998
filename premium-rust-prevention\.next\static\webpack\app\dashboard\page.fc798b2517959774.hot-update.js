"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Header */ \"(app-pages-browser)/./src/components/Header.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Car_HelpCircle_Info_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Car,HelpCircle,Info!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/car.js\");\n/* harmony import */ var _barrel_optimize_names_Car_HelpCircle_Info_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Car,HelpCircle,Info!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_Car_HelpCircle_Info_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Car,HelpCircle,Info!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-question-mark.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction DashboardPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const handleLogout = async ()=>{\n        try {\n            await fetch('/api/auth/logout', {\n                method: 'POST'\n            });\n            // Clear localStorage data\n            localStorage.removeItem('auth_token');\n            localStorage.removeItem('user_data');\n        } catch (error) {\n            console.error('Logout error:', error);\n        } finally{\n            router.push('/'); // Redirect to main page\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-prp-dark via-gray-900 to-prp-dark\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                onLogout: handleLogout\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PRP\\\\premium-rust-prevention\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-12 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-5xl font-bold text-prp-light mb-4\",\n                                children: \"Welcome to Premium Rust Prevention!\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PRP\\\\premium-rust-prevention\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 36,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-prp-silver mb-8\",\n                                children: \"Your trusted partner in automotive protection and rust prevention solutions.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PRP\\\\premium-rust-prevention\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 39,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-prp-dark/60 border border-prp-gold/30 rounded-2xl p-8 max-w-4xl mx-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center mb-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Car_HelpCircle_Info_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"w-16 h-16 text-prp-gold\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PRP\\\\premium-rust-prevention\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 44,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PRP\\\\premium-rust-prevention\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 43,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-3xl font-bold text-prp-light mb-4\",\n                                        children: \"Dashboard\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PRP\\\\premium-rust-prevention\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 46,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-prp-silver text-lg leading-relaxed\",\n                                        children: \"You have successfully logged in to your Premium Rust Prevention account. This is your main dashboard where you can manage your automotive protection services.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PRP\\\\premium-rust-prevention\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 47,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PRP\\\\premium-rust-prevention\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 42,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PRP\\\\premium-rust-prevention\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-prp-dark/50 border border-prp-silver/20 rounded-xl p-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-prp-light mb-6 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Car_HelpCircle_Info_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-6 h-6 mr-3 text-prp-gold\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PRP\\\\premium-rust-prevention\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 59,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Quick Actions\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PRP\\\\premium-rust-prevention\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                onClick: ()=>router.push('/forms'),\n                                                variant: \"primary\",\n                                                className: \"w-full text-lg py-4\",\n                                                children: \"Submit New Form\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PRP\\\\premium-rust-prevention\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 63,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                onClick: ()=>router.push('/stats'),\n                                                variant: \"outline\",\n                                                className: \"w-full text-lg py-4\",\n                                                children: \"View Statistics\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PRP\\\\premium-rust-prevention\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 70,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PRP\\\\premium-rust-prevention\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 62,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PRP\\\\premium-rust-prevention\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-prp-dark/50 border border-prp-silver/20 rounded-xl p-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-prp-light mb-6 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Car_HelpCircle_Info_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-6 h-6 mr-3 text-prp-gold\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PRP\\\\premium-rust-prevention\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 83,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"About Premium Rust Prevention\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PRP\\\\premium-rust-prevention\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-prp-silver leading-relaxed\",\n                                        children: \"Premium Rust Prevention (PRP) is your trusted partner in automotive protection. We provide comprehensive rust prevention solutions that extend vehicle life and maintain value. Our advanced coating technology ensures maximum protection against corrosion and environmental damage.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PRP\\\\premium-rust-prevention\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PRP\\\\premium-rust-prevention\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PRP\\\\premium-rust-prevention\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-prp-dark/50 border border-prp-silver/20 rounded-xl p-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-prp-light mb-6 flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Car_HelpCircle_Info_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-6 h-6 mr-3 text-prp-gold\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PRP\\\\premium-rust-prevention\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Frequently Asked Questions\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PRP\\\\premium-rust-prevention\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-prp-light font-medium mb-2\",\n                                                    children: \"How is PRP sales calculated?\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PRP\\\\premium-rust-prevention\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 104,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-prp-silver text-sm\",\n                                                    children: \"PRP sales is calculated as 5% of the total car value. This represents your commission for selling the rust prevention service.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PRP\\\\premium-rust-prevention\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 105,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PRP\\\\premium-rust-prevention\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-prp-light font-medium mb-2\",\n                                                    children: \"How long does approval take?\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PRP\\\\premium-rust-prevention\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 111,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-prp-silver text-sm\",\n                                                    children: \"Form submissions are typically reviewed and approved within 24-48 hours. You'll receive notifications about status changes.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PRP\\\\premium-rust-prevention\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 112,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PRP\\\\premium-rust-prevention\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-prp-light font-medium mb-2\",\n                                                    children: \"Can I edit submitted forms?\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PRP\\\\premium-rust-prevention\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 118,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-prp-silver text-sm\",\n                                                    children: \"Once submitted, forms cannot be edited. Please ensure all information is accurate before submission. Contact support if corrections are needed.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PRP\\\\premium-rust-prevention\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 119,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PRP\\\\premium-rust-prevention\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-prp-light font-medium mb-2\",\n                                                    children: \"Need Support?\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PRP\\\\premium-rust-prevention\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 125,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-prp-silver text-sm\",\n                                                    children: \"Contact our support team for any questions or assistance with your rust prevention services and form submissions.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PRP\\\\premium-rust-prevention\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 126,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PRP\\\\premium-rust-prevention\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PRP\\\\premium-rust-prevention\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PRP\\\\premium-rust-prevention\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PRP\\\\premium-rust-prevention\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PRP\\\\premium-rust-prevention\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PRP\\\\premium-rust-prevention\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"fN7XvhJ+p5oE6+Xlo0NJmXpxjC8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZGFzaGJvYXJkL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7OztBQUUwQjtBQUNrQjtBQUNIO0FBQ0c7QUFLdEI7QUFFUCxTQUFTTzs7SUFDdEIsTUFBTUMsU0FBU1AsMERBQVNBO0lBRXhCLE1BQU1RLGVBQWU7UUFDbkIsSUFBSTtZQUNGLE1BQU1DLE1BQU0sb0JBQW9CO2dCQUFFQyxRQUFRO1lBQU87WUFDakQsMEJBQTBCO1lBQzFCQyxhQUFhQyxVQUFVLENBQUM7WUFDeEJELGFBQWFDLFVBQVUsQ0FBQztRQUMxQixFQUFFLE9BQU9DLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLGlCQUFpQkE7UUFDakMsU0FBVTtZQUNSTixPQUFPUSxJQUFJLENBQUMsTUFBTSx3QkFBd0I7UUFDNUM7SUFDRjtJQUVBLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVOzswQkFDYiw4REFBQ2hCLDBEQUFNQTtnQkFBQ2lCLFVBQVVWOzs7Ozs7MEJBRWxCLDhEQUFDVztnQkFBS0YsV0FBVTs7a0NBRWQsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0c7Z0NBQUdILFdBQVU7MENBQXlDOzs7Ozs7MENBR3ZELDhEQUFDSTtnQ0FBRUosV0FBVTswQ0FBK0I7Ozs7OzswQ0FHNUMsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVU7a0RBQ2IsNEVBQUNkLCtGQUFHQTs0Q0FBQ2MsV0FBVTs7Ozs7Ozs7Ozs7a0RBRWpCLDhEQUFDSzt3Q0FBR0wsV0FBVTtrREFBeUM7Ozs7OztrREFDdkQsOERBQUNJO3dDQUFFSixXQUFVO2tEQUEwQzs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQVEzRCw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUViLDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNLO3dDQUFHTCxXQUFVOzswREFDWiw4REFBQ2QsK0ZBQUdBO2dEQUFDYyxXQUFVOzs7Ozs7NENBQStCOzs7Ozs7O2tEQUdoRCw4REFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDZiw2REFBTUE7Z0RBQ0xxQixTQUFTLElBQU1oQixPQUFPUSxJQUFJLENBQUM7Z0RBQzNCUyxTQUFRO2dEQUNSUCxXQUFVOzBEQUNYOzs7Ozs7MERBR0QsOERBQUNmLDZEQUFNQTtnREFDTHFCLFNBQVMsSUFBTWhCLE9BQU9RLElBQUksQ0FBQztnREFDM0JTLFNBQVE7Z0RBQ1JQLFdBQVU7MERBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FPTCw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDSzt3Q0FBR0wsV0FBVTs7MERBQ1osOERBQUNiLCtGQUFJQTtnREFBQ2EsV0FBVTs7Ozs7OzRDQUErQjs7Ozs7OztrREFHakQsOERBQUNJO3dDQUFFSixXQUFVO2tEQUFrQzs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQVVuRCw4REFBQ0Q7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0s7b0NBQUdMLFdBQVU7O3NEQUNaLDhEQUFDWiwrRkFBVUE7NENBQUNZLFdBQVU7Ozs7Ozt3Q0FBK0I7Ozs7Ozs7OENBR3ZELDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNEOzs4REFDQyw4REFBQ1M7b0RBQUdSLFdBQVU7OERBQWtDOzs7Ozs7OERBQ2hELDhEQUFDSTtvREFBRUosV0FBVTs4REFBMEI7Ozs7Ozs7Ozs7OztzREFLekMsOERBQUNEOzs4REFDQyw4REFBQ1M7b0RBQUdSLFdBQVU7OERBQWtDOzs7Ozs7OERBQ2hELDhEQUFDSTtvREFBRUosV0FBVTs4REFBMEI7Ozs7Ozs7Ozs7OztzREFLekMsOERBQUNEOzs4REFDQyw4REFBQ1M7b0RBQUdSLFdBQVU7OERBQWtDOzs7Ozs7OERBQ2hELDhEQUFDSTtvREFBRUosV0FBVTs4REFBMEI7Ozs7Ozs7Ozs7OztzREFLekMsOERBQUNEOzs4REFDQyw4REFBQ1M7b0RBQUdSLFdBQVU7OERBQWtDOzs7Ozs7OERBQ2hELDhEQUFDSTtvREFBRUosV0FBVTs4REFBMEI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBV3ZEO0dBNUh3Qlg7O1FBQ1BOLHNEQUFTQTs7O0tBREZNIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXERFTExcXERlc2t0b3BcXFBSUFxccHJlbWl1bS1ydXN0LXByZXZlbnRpb25cXHNyY1xcYXBwXFxkYXNoYm9hcmRcXHBhZ2UudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHVzZVJvdXRlciB9IGZyb20gJ25leHQvbmF2aWdhdGlvbic7XG5pbXBvcnQgSGVhZGVyIGZyb20gJ0AvY29tcG9uZW50cy9IZWFkZXInO1xuaW1wb3J0IEJ1dHRvbiBmcm9tICdAL2NvbXBvbmVudHMvdWkvQnV0dG9uJztcbmltcG9ydCB7XG4gIENhcixcbiAgSW5mbyxcbiAgSGVscENpcmNsZVxufSBmcm9tICdsdWNpZGUtcmVhY3QnO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBEYXNoYm9hcmRQYWdlKCkge1xuICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKTtcblxuICBjb25zdCBoYW5kbGVMb2dvdXQgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGF3YWl0IGZldGNoKCcvYXBpL2F1dGgvbG9nb3V0JywgeyBtZXRob2Q6ICdQT1NUJyB9KTtcbiAgICAgIC8vIENsZWFyIGxvY2FsU3RvcmFnZSBkYXRhXG4gICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgnYXV0aF90b2tlbicpO1xuICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oJ3VzZXJfZGF0YScpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdMb2dvdXQgZXJyb3I6JywgZXJyb3IpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICByb3V0ZXIucHVzaCgnLycpOyAvLyBSZWRpcmVjdCB0byBtYWluIHBhZ2VcbiAgICB9XG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1ncmFkaWVudC10by1iciBmcm9tLXBycC1kYXJrIHZpYS1ncmF5LTkwMCB0by1wcnAtZGFya1wiPlxuICAgICAgPEhlYWRlciBvbkxvZ291dD17aGFuZGxlTG9nb3V0fSAvPlxuXG4gICAgICA8bWFpbiBjbGFzc05hbWU9XCJtYXgtdy03eGwgbXgtYXV0byBweC00IHNtOnB4LTYgbGc6cHgtOCBweS04XCI+XG4gICAgICAgIHsvKiBXZWxjb21lIFNlY3Rpb24gKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItMTIgdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC01eGwgZm9udC1ib2xkIHRleHQtcHJwLWxpZ2h0IG1iLTRcIj5cbiAgICAgICAgICAgIFdlbGNvbWUgdG8gUHJlbWl1bSBSdXN0IFByZXZlbnRpb24hXG4gICAgICAgICAgPC9oMT5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhsIHRleHQtcHJwLXNpbHZlciBtYi04XCI+XG4gICAgICAgICAgICBZb3VyIHRydXN0ZWQgcGFydG5lciBpbiBhdXRvbW90aXZlIHByb3RlY3Rpb24gYW5kIHJ1c3QgcHJldmVudGlvbiBzb2x1dGlvbnMuXG4gICAgICAgICAgPC9wPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctcHJwLWRhcmsvNjAgYm9yZGVyIGJvcmRlci1wcnAtZ29sZC8zMCByb3VuZGVkLTJ4bCBwLTggbWF4LXctNHhsIG14LWF1dG9cIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbWItNlwiPlxuICAgICAgICAgICAgICA8Q2FyIGNsYXNzTmFtZT1cInctMTYgaC0xNiB0ZXh0LXBycC1nb2xkXCIgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZCB0ZXh0LXBycC1saWdodCBtYi00XCI+RGFzaGJvYXJkPC9oMj5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtcHJwLXNpbHZlciB0ZXh0LWxnIGxlYWRpbmctcmVsYXhlZFwiPlxuICAgICAgICAgICAgICBZb3UgaGF2ZSBzdWNjZXNzZnVsbHkgbG9nZ2VkIGluIHRvIHlvdXIgUHJlbWl1bSBSdXN0IFByZXZlbnRpb24gYWNjb3VudC5cbiAgICAgICAgICAgICAgVGhpcyBpcyB5b3VyIG1haW4gZGFzaGJvYXJkIHdoZXJlIHlvdSBjYW4gbWFuYWdlIHlvdXIgYXV0b21vdGl2ZSBwcm90ZWN0aW9uIHNlcnZpY2VzLlxuICAgICAgICAgICAgPC9wPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogTWFpbiBDb250ZW50IEdyaWQgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBsZzpncmlkLWNvbHMtMiBnYXAtOFwiPlxuICAgICAgICAgIHsvKiBRdWljayBBY3Rpb25zICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctcHJwLWRhcmsvNTAgYm9yZGVyIGJvcmRlci1wcnAtc2lsdmVyLzIwIHJvdW5kZWQteGwgcC04XCI+XG4gICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtcHJwLWxpZ2h0IG1iLTYgZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgPENhciBjbGFzc05hbWU9XCJ3LTYgaC02IG1yLTMgdGV4dC1wcnAtZ29sZFwiIC8+XG4gICAgICAgICAgICAgIFF1aWNrIEFjdGlvbnNcbiAgICAgICAgICAgIDwvaDI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gcm91dGVyLnB1c2goJy9mb3JtcycpfVxuICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJwcmltYXJ5XCJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgdGV4dC1sZyBweS00XCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIFN1Ym1pdCBOZXcgRm9ybVxuICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHJvdXRlci5wdXNoKCcvc3RhdHMnKX1cbiAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHRleHQtbGcgcHktNFwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICBWaWV3IFN0YXRpc3RpY3NcbiAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBBYm91dCBVcyBTZWN0aW9uICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctcHJwLWRhcmsvNTAgYm9yZGVyIGJvcmRlci1wcnAtc2lsdmVyLzIwIHJvdW5kZWQteGwgcC04XCI+XG4gICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtcHJwLWxpZ2h0IG1iLTYgZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgPEluZm8gY2xhc3NOYW1lPVwidy02IGgtNiBtci0zIHRleHQtcHJwLWdvbGRcIiAvPlxuICAgICAgICAgICAgICBBYm91dCBQcmVtaXVtIFJ1c3QgUHJldmVudGlvblxuICAgICAgICAgICAgPC9oMj5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtcHJwLXNpbHZlciBsZWFkaW5nLXJlbGF4ZWRcIj5cbiAgICAgICAgICAgICAgUHJlbWl1bSBSdXN0IFByZXZlbnRpb24gKFBSUCkgaXMgeW91ciB0cnVzdGVkIHBhcnRuZXIgaW4gYXV0b21vdGl2ZSBwcm90ZWN0aW9uLlxuICAgICAgICAgICAgICBXZSBwcm92aWRlIGNvbXByZWhlbnNpdmUgcnVzdCBwcmV2ZW50aW9uIHNvbHV0aW9ucyB0aGF0IGV4dGVuZCB2ZWhpY2xlIGxpZmUgYW5kXG4gICAgICAgICAgICAgIG1haW50YWluIHZhbHVlLiBPdXIgYWR2YW5jZWQgY29hdGluZyB0ZWNobm9sb2d5IGVuc3VyZXMgbWF4aW11bSBwcm90ZWN0aW9uIGFnYWluc3RcbiAgICAgICAgICAgICAgY29ycm9zaW9uIGFuZCBlbnZpcm9ubWVudGFsIGRhbWFnZS5cbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIEZBUSBTZWN0aW9uICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTEyXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1wcnAtZGFyay81MCBib3JkZXIgYm9yZGVyLXBycC1zaWx2ZXIvMjAgcm91bmRlZC14bCBwLThcIj5cbiAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1wcnAtbGlnaHQgbWItNiBmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICA8SGVscENpcmNsZSBjbGFzc05hbWU9XCJ3LTYgaC02IG1yLTMgdGV4dC1wcnAtZ29sZFwiIC8+XG4gICAgICAgICAgICAgIEZyZXF1ZW50bHkgQXNrZWQgUXVlc3Rpb25zXG4gICAgICAgICAgICA8L2gyPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGdhcC02XCI+XG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtcHJwLWxpZ2h0IGZvbnQtbWVkaXVtIG1iLTJcIj5Ib3cgaXMgUFJQIHNhbGVzIGNhbGN1bGF0ZWQ/PC9oMz5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXBycC1zaWx2ZXIgdGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgICAgUFJQIHNhbGVzIGlzIGNhbGN1bGF0ZWQgYXMgNSUgb2YgdGhlIHRvdGFsIGNhciB2YWx1ZS4gVGhpcyByZXByZXNlbnRzIHlvdXIgY29tbWlzc2lvblxuICAgICAgICAgICAgICAgICAgZm9yIHNlbGxpbmcgdGhlIHJ1c3QgcHJldmVudGlvbiBzZXJ2aWNlLlxuICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtcHJwLWxpZ2h0IGZvbnQtbWVkaXVtIG1iLTJcIj5Ib3cgbG9uZyBkb2VzIGFwcHJvdmFsIHRha2U/PC9oMz5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXBycC1zaWx2ZXIgdGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgICAgRm9ybSBzdWJtaXNzaW9ucyBhcmUgdHlwaWNhbGx5IHJldmlld2VkIGFuZCBhcHByb3ZlZCB3aXRoaW4gMjQtNDggaG91cnMuXG4gICAgICAgICAgICAgICAgICBZb3UnbGwgcmVjZWl2ZSBub3RpZmljYXRpb25zIGFib3V0IHN0YXR1cyBjaGFuZ2VzLlxuICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtcHJwLWxpZ2h0IGZvbnQtbWVkaXVtIG1iLTJcIj5DYW4gSSBlZGl0IHN1Ym1pdHRlZCBmb3Jtcz88L2gzPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtcHJwLXNpbHZlciB0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgICAgICBPbmNlIHN1Ym1pdHRlZCwgZm9ybXMgY2Fubm90IGJlIGVkaXRlZC4gUGxlYXNlIGVuc3VyZSBhbGwgaW5mb3JtYXRpb24gaXMgYWNjdXJhdGVcbiAgICAgICAgICAgICAgICAgIGJlZm9yZSBzdWJtaXNzaW9uLiBDb250YWN0IHN1cHBvcnQgaWYgY29ycmVjdGlvbnMgYXJlIG5lZWRlZC5cbiAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXBycC1saWdodCBmb250LW1lZGl1bSBtYi0yXCI+TmVlZCBTdXBwb3J0PzwvaDM+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1wcnAtc2lsdmVyIHRleHQtc21cIj5cbiAgICAgICAgICAgICAgICAgIENvbnRhY3Qgb3VyIHN1cHBvcnQgdGVhbSBmb3IgYW55IHF1ZXN0aW9ucyBvciBhc3Npc3RhbmNlIHdpdGggeW91ciBydXN0IHByZXZlbnRpb25cbiAgICAgICAgICAgICAgICAgIHNlcnZpY2VzIGFuZCBmb3JtIHN1Ym1pc3Npb25zLlxuICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L21haW4+XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VSb3V0ZXIiLCJIZWFkZXIiLCJCdXR0b24iLCJDYXIiLCJJbmZvIiwiSGVscENpcmNsZSIsIkRhc2hib2FyZFBhZ2UiLCJyb3V0ZXIiLCJoYW5kbGVMb2dvdXQiLCJmZXRjaCIsIm1ldGhvZCIsImxvY2FsU3RvcmFnZSIsInJlbW92ZUl0ZW0iLCJlcnJvciIsImNvbnNvbGUiLCJwdXNoIiwiZGl2IiwiY2xhc3NOYW1lIiwib25Mb2dvdXQiLCJtYWluIiwiaDEiLCJwIiwiaDIiLCJvbkNsaWNrIiwidmFyaWFudCIsImgzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ })

});