'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import Header from '@/components/Header';
import Button from '@/components/ui/Button';
import {
  BarChart3,
  TrendingUp
} from 'lucide-react';

export default function StatsPage() {
  const router = useRouter();

  const handleLogout = async () => {
    try {
      await fetch('/api/auth/logout', { method: 'POST' });
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      router.push('/login');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-prp-dark via-gray-900 to-prp-dark">
      <Header onLogout={handleLogout} />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-12 text-center">
          <h1 className="text-5xl font-bold text-prp-light mb-4 flex items-center justify-center">
            <BarChart3 className="w-12 h-12 mr-4 text-prp-gold" />
            Statistics Page
          </h1>
          <p className="text-xl text-prp-silver mb-8">
            This is a simple statistics page for Premium Rust Prevention.
          </p>
          <div className="bg-prp-dark/60 border border-prp-gold/30 rounded-2xl p-8 max-w-4xl mx-auto">
            <div className="flex items-center justify-center mb-6">
              <TrendingUp className="w-16 h-16 text-prp-gold" />
            </div>
            <h2 className="text-3xl font-bold text-prp-light mb-4">Coming Soon</h2>
            <p className="text-prp-silver text-lg leading-relaxed">
              Detailed statistics and analytics will be available here. This page will show
              comprehensive data about your rust prevention services and performance metrics.
            </p>
          </div>
        </div>

        {/* Simple Action Buttons */}
        <div className="flex justify-center space-x-6">
          <Button
            onClick={() => router.push('/dashboard')}
            variant="primary"
            className="text-lg py-4 px-8"
          >
            Back to Dashboard
          </Button>
          <Button
            onClick={() => router.push('/forms')}
            variant="outline"
            className="text-lg py-4 px-8"
          >
            Submit Form
          </Button>
        </div>
      </main>
    </div>
  );
}
