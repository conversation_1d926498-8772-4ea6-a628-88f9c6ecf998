'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Logo from '@/components/Logo';
import Button from '@/components/ui/Button';

export default function Home() {
  const router = useRouter();

  useEffect(() => {
    // Check if user is already logged in
    const token = localStorage.getItem('auth_token');
    if (token) {
      router.push('/dashboard');
    }
  }, [router]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-prp-dark via-gray-900 to-prp-dark flex items-center justify-center px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto text-center">
        {/* Logo and Header */}
        <div className="mb-12">
          <div className="flex justify-center mb-8">
            <Logo size="lg" showText={true} />
          </div>
          <h1 className="text-4xl md:text-6xl font-bold text-prp-light mb-6">
            Professional Vehicle
            <span className="block text-prp-gold">Rust Prevention</span>
          </h1>
          <p className="text-xl text-prp-silver max-w-2xl mx-auto leading-relaxed">
            Advanced coating technology for automotive dealerships.
            Track your sales, manage submissions, and protect vehicles with our comprehensive rust prevention solutions.
          </p>
        </div>

        {/* Features */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
          <div className="bg-prp-dark/60 border border-prp-silver/30 rounded-2xl p-8 shadow-xl hover:shadow-2xl transition-all duration-300 hover:border-prp-gold/50">
            <div className="w-12 h-12 bg-prp-gold/20 rounded-lg flex items-center justify-center mx-auto mb-4">
              <svg className="w-6 h-6 text-prp-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-prp-light mb-2">Easy Tracking</h3>
            <p className="text-prp-silver text-sm">
              Submit and track your vehicle forms with real-time status updates and comprehensive statistics.
            </p>
          </div>

          <div className="bg-prp-dark/60 border border-prp-silver/30 rounded-2xl p-8 shadow-xl hover:shadow-2xl transition-all duration-300 hover:border-prp-gold/50">
            <div className="w-12 h-12 bg-prp-gold/20 rounded-lg flex items-center justify-center mx-auto mb-4">
              <svg className="w-6 h-6 text-prp-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-prp-light mb-2">Commission Tracking</h3>
            <p className="text-prp-silver text-sm">
              Automatically calculate your 5% commission on total vehicle value with detailed earnings reports.
            </p>
          </div>

          <div className="bg-prp-dark/60 border border-prp-silver/30 rounded-2xl p-8 shadow-xl hover:shadow-2xl transition-all duration-300 hover:border-prp-gold/50">
            <div className="w-12 h-12 bg-prp-gold/20 rounded-lg flex items-center justify-center mx-auto mb-4">
              <svg className="w-6 h-6 text-prp-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-prp-light mb-2">Premium Protection</h3>
            <p className="text-prp-silver text-sm">
              Industry-leading rust prevention technology that extends vehicle life and maintains value.
            </p>
          </div>
        </div>

        {/* CTA Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button
            onClick={() => router.push('/signup')}
            variant="primary"
            size="lg"
            className="px-8 py-4 text-lg"
          >
            Get Started Today
          </Button>
          <Button
            onClick={() => router.push('/login')}
            variant="outline"
            size="lg"
            className="px-8 py-4 text-lg"
          >
            Sign In
          </Button>
        </div>

        {/* Footer */}
        <div className="mt-16 pt-8 border-t border-prp-silver/20">
          <p className="text-prp-silver/70 text-sm">
            © 2024 Premium Rust Prevention. Professional vehicle protection solutions.
          </p>
        </div>
      </div>
    </div>
  );
}
