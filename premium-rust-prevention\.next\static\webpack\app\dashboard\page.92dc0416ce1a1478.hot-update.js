"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/hooks/useAuth.ts":
/*!******************************!*\
  !*** ./src/hooks/useAuth.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _utils_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/auth */ \"(app-pages-browser)/./src/utils/auth.ts\");\n/* __next_internal_client_entry_do_not_use__ useAuth auto */ \n\n\nconst useAuth = ()=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAuth.useEffect\": ()=>{\n            const token = (0,_utils_auth__WEBPACK_IMPORTED_MODULE_2__.getAuthToken)();\n            const userData = (0,_utils_auth__WEBPACK_IMPORTED_MODULE_2__.getUserData)();\n            if (token && userData) {\n                setUser(userData);\n            }\n            setIsLoading(false);\n        }\n    }[\"useAuth.useEffect\"], []);\n    const logout = async ()=>{\n        try {\n            await fetch('/api/auth/logout', {\n                method: 'POST'\n            });\n        } catch (error) {\n            console.error('Logout error:', error);\n        }\n        (0,_utils_auth__WEBPACK_IMPORTED_MODULE_2__.clearAuthData)();\n        setUser(null);\n        router.push('/'); // Redirect to main page instead of login\n    };\n    const updateUser = (userData)=>{\n        setUser(userData);\n    };\n    return {\n        user,\n        isLoading,\n        isAuthenticated: !!user,\n        logout,\n        updateUser\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9ob29rcy91c2VBdXRoLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OzZEQUU0QztBQUNBO0FBQ2tDO0FBRXZFLE1BQU1NLFVBQVU7SUFDckIsTUFBTSxDQUFDQyxNQUFNQyxRQUFRLEdBQUdSLCtDQUFRQSxDQUFjO0lBQzlDLE1BQU0sQ0FBQ1MsV0FBV0MsYUFBYSxHQUFHViwrQ0FBUUEsQ0FBQztJQUMzQyxNQUFNVyxTQUFTVCwwREFBU0E7SUFFeEJELGdEQUFTQTs2QkFBQztZQUNSLE1BQU1XLFFBQVFSLHlEQUFZQTtZQUMxQixNQUFNUyxXQUFXVix3REFBV0E7WUFFNUIsSUFBSVMsU0FBU0MsVUFBVTtnQkFDckJMLFFBQVFLO1lBQ1Y7WUFFQUgsYUFBYTtRQUNmOzRCQUFHLEVBQUU7SUFFTCxNQUFNSSxTQUFTO1FBQ2IsSUFBSTtZQUNGLE1BQU1DLE1BQU0sb0JBQW9CO2dCQUFFQyxRQUFRO1lBQU87UUFDbkQsRUFBRSxPQUFPQyxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyxpQkFBaUJBO1FBQ2pDO1FBRUFaLDBEQUFhQTtRQUNiRyxRQUFRO1FBQ1JHLE9BQU9RLElBQUksQ0FBQyxNQUFNLHlDQUF5QztJQUM3RDtJQUVBLE1BQU1DLGFBQWEsQ0FBQ1A7UUFDbEJMLFFBQVFLO0lBQ1Y7SUFFQSxPQUFPO1FBQ0xOO1FBQ0FFO1FBQ0FZLGlCQUFpQixDQUFDLENBQUNkO1FBQ25CTztRQUNBTTtJQUNGO0FBQ0YsRUFBRSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxERUxMXFxEZXNrdG9wXFxQUlBcXHByZW1pdW0tcnVzdC1wcmV2ZW50aW9uXFxzcmNcXGhvb2tzXFx1c2VBdXRoLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHVzZVJvdXRlciB9IGZyb20gJ25leHQvbmF2aWdhdGlvbic7XG5pbXBvcnQgeyBVc2VyLCBnZXRVc2VyRGF0YSwgZ2V0QXV0aFRva2VuLCBjbGVhckF1dGhEYXRhIH0gZnJvbSAnQC91dGlscy9hdXRoJztcblxuZXhwb3J0IGNvbnN0IHVzZUF1dGggPSAoKSA9PiB7XG4gIGNvbnN0IFt1c2VyLCBzZXRVc2VyXSA9IHVzZVN0YXRlPFVzZXIgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW2lzTG9hZGluZywgc2V0SXNMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpO1xuICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IHRva2VuID0gZ2V0QXV0aFRva2VuKCk7XG4gICAgY29uc3QgdXNlckRhdGEgPSBnZXRVc2VyRGF0YSgpO1xuXG4gICAgaWYgKHRva2VuICYmIHVzZXJEYXRhKSB7XG4gICAgICBzZXRVc2VyKHVzZXJEYXRhKTtcbiAgICB9XG4gICAgXG4gICAgc2V0SXNMb2FkaW5nKGZhbHNlKTtcbiAgfSwgW10pO1xuXG4gIGNvbnN0IGxvZ291dCA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgYXdhaXQgZmV0Y2goJy9hcGkvYXV0aC9sb2dvdXQnLCB7IG1ldGhvZDogJ1BPU1QnIH0pO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdMb2dvdXQgZXJyb3I6JywgZXJyb3IpO1xuICAgIH1cblxuICAgIGNsZWFyQXV0aERhdGEoKTtcbiAgICBzZXRVc2VyKG51bGwpO1xuICAgIHJvdXRlci5wdXNoKCcvJyk7IC8vIFJlZGlyZWN0IHRvIG1haW4gcGFnZSBpbnN0ZWFkIG9mIGxvZ2luXG4gIH07XG5cbiAgY29uc3QgdXBkYXRlVXNlciA9ICh1c2VyRGF0YTogVXNlcikgPT4ge1xuICAgIHNldFVzZXIodXNlckRhdGEpO1xuICB9O1xuXG4gIHJldHVybiB7XG4gICAgdXNlcixcbiAgICBpc0xvYWRpbmcsXG4gICAgaXNBdXRoZW50aWNhdGVkOiAhIXVzZXIsXG4gICAgbG9nb3V0LFxuICAgIHVwZGF0ZVVzZXJcbiAgfTtcbn07XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJ1c2VSb3V0ZXIiLCJnZXRVc2VyRGF0YSIsImdldEF1dGhUb2tlbiIsImNsZWFyQXV0aERhdGEiLCJ1c2VBdXRoIiwidXNlciIsInNldFVzZXIiLCJpc0xvYWRpbmciLCJzZXRJc0xvYWRpbmciLCJyb3V0ZXIiLCJ0b2tlbiIsInVzZXJEYXRhIiwibG9nb3V0IiwiZmV0Y2giLCJtZXRob2QiLCJlcnJvciIsImNvbnNvbGUiLCJwdXNoIiwidXBkYXRlVXNlciIsImlzQXV0aGVudGljYXRlZCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useAuth.ts\n"));

/***/ })

});