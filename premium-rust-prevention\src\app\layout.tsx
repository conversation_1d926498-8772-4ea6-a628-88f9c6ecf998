import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Premium Rust Prevention - Professional Vehicle Protection",
  description: "Premium Rust Prevention (PRP) provides comprehensive rust prevention solutions for automotive dealerships and salespeople. Track your sales, manage submissions, and protect vehicles with our advanced coating technology.",
  keywords: "rust prevention, vehicle protection, automotive, dealership, car protection, PRP",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${inter.variable} font-sans antialiased`}>
        {children}
      </body>
    </html>
  );
}
